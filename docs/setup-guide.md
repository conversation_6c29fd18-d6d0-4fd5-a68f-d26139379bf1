# Argus Cloud Connector Setup Guide

## Overview

This is an Argus Cloud Connector application that bridges communication between two MQTT brokers:

1. A server broker (iiop-dev.aatmunn.net) for Argus platform communication
2. A client broker ([localhost](localhost)) for device communication

## Prerequisites

-   No programming language installation required
-   MQTT brokers accessible
-   Basic text editor for configuration files

## Quick Start

1. **Download and Extract**

    - Download the `argus-cloud-connector` binary
    - Extract it to your desired location

2. **Configuration Files**
   Place these configuration files in the `configs/` directory:

    a. **Server Configuration** (`configs/config-server.json`)
    b. **Client Configuration** (`configs/config-client.json`)
    c. **Firmware Configuration** (`configs/firmware-config.json`)
    d. **Mapping Configuration** (`configs/mapping-config.json`)

    The firmware configuration file contains the following key sections:

    1. **Basic Information**

        - `zoneName`: Identifier for the zone (e.g., "zoneA")
        - `deviceId`: Unique identifier for the device

    2. **MQTT Configuration**

        - `mqttConf`: MQTT broker connection details
            - `host`: Broker host address (default: "127.0.0.1")
            - `port`: Broker port number (default: 1883)
            - `username`: Authentication username (default: "admin")
            - `password`: Authentication password (default: "admin")

    3. **Retry Configuration**

        - `retry`: Retry configuration for MQTT connections
            - `count`: Number of retry attempts (default: 3)
            - `waitTime`: Time to wait between retry attempts (in milliseconds, default: 5000)

    4. **MQTT Topics**

        - `mqttTopics`: Defines all MQTT topics for communication
            - `readReqTopic`: Topic for reading single channel ("readData/req")
            - `readResTopic`: Topic for read response ("readData/res")
            - `writeReqTopic`: Topic for writing to channels ("writeData/req")
            - `writeResTopic`: Topic for write response ("writeData/res")
            - `periodicResTopic`: Topic for periodic data ("readData/periodic")
            - `periodicityInterval`: Interval for periodic updates (in milliseconds, default: 5000)

    5. **Port Configuration**

        - `ports`: Array of serial port configurations
            - `baudRate`: Communication speed (default: 115200)
            - `parity`: Parity setting (default: "NONE")
            - `cards`: Array of connected cards

    6. **Card Configuration**

        - `cardId`: Unique identifier for the card (e.g., "CD1")
        - `modbusAddr`: Modbus address in hexadecimal (e.g., "0x23")
        - `channels`: Array of channels on the card

    7. **Channel Configuration**
        - `channelId`: Unique identifier for the channel (e.g., "CH1")
        - `channelName`: Human-readable name (e.g., "Unit Heater")
        - `type`: Channel type ("input" or "output")
        - `signalType`: Type of signal
            - Digital: "digital"
            - Analog: "analog"
        - `value`: Channel value configuration
            - `address`: Modbus address in hexadecimal
            - `bytes`: Number of bytes to read/write
        - `range`: Optional range configuration for analog signals
            - `min`: Minimum value
            - `max`: Maximum value

    **Important Notes:**

    1. Keep the `mqttTopics` section unchanged for POC
    2. Ensure unique `cardId` and `channelId` values
    3. Match `signalType` with your hardware specifications
    4. Set appropriate `baudRate` and `parity` for your serial connection
    5. Verify `modbusAddr` matches your hardware configuration
    6. For analog signals, always include the `range` configuration
    7. The `type` field must be either "input" or "output"
    8. The `bytes` field should match your Modbus register size

    **Example Channel Types:**

    - Digital Output: `{"type": "output", "signalType": "digital"}`
    - Analog Input: `{"type": "input", "signalType": "analog", "range": {"min": 0, "max": 100}}`

    Modify this configuration only if your device configuration differs from the default. Ensure all required fields are present and properly formatted.

3. **Run the Application**

    ```bash
    # On Linux/Mac
    ./argus-cloud-connector

    # On Windows
    argus-cloud-connector.exe
    ```

## Optional Configuration

### 1. Server Configuration (`configs/config-server.json`)

Modify if you need to:

-   Change the MQTT broker address
-   Update credentials
-   Change the client ID

> for POC purpose you cannot change topics

### 2. Client Configuration (`configs/config-client.json`)

Modify if you need to:

-   Change the MQTT broker address
-   Update the client ID

> for POC purpose you cannot change topics

### 3. Firmware Configuration (`configs/firmware-config.json`)

Modify if you need to:

-   Update zone information
-   Change device ID
-   Modify port configurations
-   Update card and channel definitions
-   Change signal types and ranges

### 4. Mapping Configuration (`configs/mapping-config.json`)

Modify if you need to:

-   Update card to zone mappings
-   Change zone ID to name mappings
-   Update zone ID to hub mappings

## Verifying Operation

1. **Check Logs**

    - The application will display connection status
    - Shows subscribed topics
    - Displays message flow information

2. **Expected Output**

    ```
    Loaded MQTT configurations:
    Server Config:
    Broker: rado-dev-broker.1iot.io:8885
    Username: platform_user
    Client ID: go-mqtt-server
    Subscribe topics: 3
    Publish topics: 3

    Client Config:
    Broker: paradox-dev-broker.1iot.io:8885
    Username: platform_user
    Client ID: go-mqtt-client
    Subscribe topics: 3
    Publish topics: 2

    Connecting to MQTT broker (server) - attempt 1/5...
    Successfully connected to MQTT broker (server)
    Connecting to MQTT broker (client) - attempt 1/5...

    Subscribing to 3 server topics...
    ✓ Subscribed to server topic: argus/bangalore/aatmunnOffice/pullSensorData
    ✓ Subscribed to server topic: argus/bangalore/aatmunnOffice/reqControlData
    ✓ Subscribed to server topic: argus/bangalore/aatmunnOffice/getConfig
    ✓ Published initial config to 'argus/bangalore/aatmunnOffice/config'
    Successfully connected to MQTT broker (client)

    Subscribing to 3 client topics...
    ✓ Subscribed to client topic: readData/periodic/res
    ✓ Subscribed to client topic: readData/res
    ✓ Subscribed to client topic: writeData/res

    MQTT clients are running and listening for messages...
    Server subscribed topics:
    - TopicArgusPullSensorData: argus/bangalore/aatmunnOffice/pullSensorData
    - TopicArgusReqControlData: argus/bangalore/aatmunnOffice/reqControlData
    - TopicArgusGetConfig: argus/bangalore/aatmunnOffice/getConfig

    Client subscribed topics:
    - TopicFirmwareReadDataPeriodicRes: readData/periodic/res
    - TopicFirmwareReadDataRes: readData/res
    - TopicFirmwareWriteDataRes: writeData/res

    ```

## Troubleshooting

1. **Connection Issues**

    - Verify broker addresses and ports
    - Check network connectivity
    - Validate credentials in `configs/config-server.json`

2. **Message Flow Issues**

    - Verify topic subscriptions in configuration files
    - Check message formats
    - Monitor application logs

3. **Configuration Issues**
    - Ensure JSON files are properly formatted
    - Check file permissions
    - Verify configuration values

## Security Notes

1. **Server Connection**

    - Uses MQTT over TLS (mqtts)
    - Requires username/password authentication
    - Port 8883 for secure communication

2. **Client Connection**
    - Uses standard MQTT
    - No authentication required
    - Port 1883 for standard communication

## Stopping the Application

1. **Graceful Shutdown**
    - Press `Ctrl+C` to stop the application
    - The application will:
        - Unsubscribe from all topics
        - Disconnect from both MQTT brokers
        - Clean up resources

## Support

For issues or questions:

1. Check the application logs
2. Verify configuration files
3. Ensure network connectivity to both MQTT brokers
4. Validate message formats and topics

This simplified setup guide focuses on using the pre-built binary and configuration files. Let me know if you need any clarification or have specific questions about the setup process.
