package aggregatorClient

import (
	"argus-cloud-connector/shared"
	"encoding/json"
	"log"
	"strings"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// AggregatorClientImpl implements the AggregatorClientModule interface
type AggregatorClientImpl struct {
	config            shared.Config
	client            mqtt.Client
	serverWrapper     *shared.MQTTClientWrapper
	ioChannel         shared.AggregatorToIOChannel
	aggregatorChannel *AggregatorChannelImpl
	dataTransformer   shared.DataTransformer
	validator         shared.ValidationService
	configManager     shared.ConfigurationManager
	periodicDataStore shared.PeriodicDataStore
	periodicDataMutex sync.Mutex
	isRunning         bool
}

// NewAggregatorClient creates a new aggregator client instance
func NewAggregatorClient(dataTransformer shared.DataTransformer, validator shared.ValidationService, configManager shared.ConfigurationManager) *AggregatorClientImpl {
	aggregatorChannel := &AggregatorChannelImpl{
		periodicDataChan:  make(chan shared.CloudPeriodicData, 100),
		writeResponseChan: make(chan shared.CloudPeriodicData, 100),
		readResponseChan:  make(chan shared.CloudPeriodicData, 100),
		closeChan:         make(chan bool, 1),
	}

	return &AggregatorClientImpl{
		aggregatorChannel: aggregatorChannel,
		dataTransformer:   dataTransformer,
		validator:         validator,
		configManager:     configManager,
		isRunning:         false,
	}
}

// Initialize initializes the aggregator client with configuration
func (ac *AggregatorClientImpl) Initialize(config shared.Config) error {
	ac.config = config

	// Create MQTT client wrapper for cloud communication
	ac.serverWrapper = shared.NewMQTTClientWrapper(
		config,
		config.MQTT.ClientID+"-aggregator-client",
		ac.handleCloudMessage,
		ac.onConnect,
		ac.onConnectionLost,
		"Aggregator-client",
	)

	ac.client = ac.serverWrapper.GetClient()
	ac.aggregatorChannel.aggregatorClient = ac

	log.Printf("✓ Aggregator Client initialized")
	return nil
}

// Start starts the aggregator client
func (ac *AggregatorClientImpl) Start() error {
	if ac.isRunning {
		return nil
	}

	// Connect to cloud MQTT broker
	if err := ac.serverWrapper.Connect(); err != nil {
		return err
	}

	// Start processing data from IO
	ac.aggregatorChannel.StartProcessing()

	ac.isRunning = true
	log.Printf("✓ Aggregator Client started")
	return nil
}

// Stop stops the aggregator client
func (ac *AggregatorClientImpl) Stop() error {
	if !ac.isRunning {
		return nil
	}

	ac.isRunning = false
	ac.serverWrapper.UnsubscribeFromTopics()
	ac.serverWrapper.Disconnect()

	// Close channels
	ac.aggregatorChannel.Close()

	log.Printf("✓ Aggregator Client stopped")
	return nil
}

// SetIOChannel sets the channel to communicate with IO
func (ac *AggregatorClientImpl) SetIOChannel(channel shared.AggregatorToIOChannel) {
	ac.ioChannel = channel
}

// GetAggregatorChannel returns the channel for IO to send data to aggregator
func (ac *AggregatorClientImpl) GetAggregatorChannel() shared.IOToAggregatorChannel {
	return ac.aggregatorChannel
}

// handleCloudMessage handles messages from cloud
func (ac *AggregatorClientImpl) handleCloudMessage(client mqtt.Client, msg mqtt.Message) {
	log.Printf("Aggregator: Received from cloud topic '%s': %s\n", msg.Topic(), string(msg.Payload()))

	if err := ac.HandleCloudMessage(msg.Topic(), msg.Payload()); err != nil {
		log.Printf("Aggregator: Error handling cloud message: %v", err)
	}
}

// HandleCloudMessage processes messages from cloud
func (ac *AggregatorClientImpl) HandleCloudMessage(topic string, payload []byte) error {
	// Get topic constants
	topicArgusGetConfig := ac.getTopicConstant("TopicArgusGetConfig")
	topicArgusReqControlData := ac.getTopicConstant("TopicArgusReqControlData")
	topicArgusPullSensorData := ac.getTopicConstant("TopicArgusPullSensorData")

	switch topic {
	case topicArgusGetConfig:
		return ac.handleGetConfigRequest(payload)
	case topicArgusReqControlData:
		return ac.handleControlRequest(payload)
	case topicArgusPullSensorData:
		return ac.handlePullSensorData(payload)
	default:
		log.Printf("Aggregator: Unknown cloud topic: %s", topic)
	}

	return nil
}

// handleGetConfigRequest handles get config requests from cloud
func (ac *AggregatorClientImpl) handleGetConfigRequest(payload []byte) error {
	var getConfigReq shared.GetConfigRequest
	if err := json.Unmarshal(payload, &getConfigReq); err != nil {
		return err
	}

	log.Printf("Aggregator: Processing getConfig request: type=%s, value=%s",
		getConfigReq.RequestType, getConfigReq.Value)

	// Load IO firmware config
	ioConfig, err := ac.configManager.GetIOConfig()
	if err != nil {
		log.Printf("Aggregator: Error loading IO firmware config: %v", err)
		return err
	}

	// Transform to cloud platform format
	cloudConfig := ac.dataTransformer.TransformConfigToCloud(ioConfig)

	// Filter based on request type
	if getConfigReq.RequestType == "zone" {
		cloudConfig = ac.filterConfigByZone(cloudConfig, getConfigReq.Value)
	}

	// Send to cloud
	topic := ac.getTopicConstant("TopicArgusConfig")
	return ac.SendToCloud(topic, cloudConfig)
}

// handleControlRequest handles control requests from cloud
func (ac *AggregatorClientImpl) handleControlRequest(payload []byte) error {
	var controlReq shared.ControlRequest
	if err := json.Unmarshal(payload, &controlReq); err != nil {
		return err
	}

	// Validate the control request
	if err := ac.validator.ValidateControlRequest(controlReq); err != nil {
		log.Printf("Aggregator: Control request validation failed: %v", err)
		return err
	}

	// Create write request
	writeReq := shared.WriteRequest{
		Cards: []struct {
			CardID   string `json:"cardId"`
			Channels []struct {
				ChannelID   string      `json:"channelId"`
				ChannelName string      `json:"channelName"`
				Value       interface{} `json:"value"`
				Unit        string      `json:"unit"`
			} `json:"channels"`
		}{
			{
				CardID: controlReq.CardID,
				Channels: []struct {
					ChannelID   string      `json:"channelId"`
					ChannelName string      `json:"channelName"`
					Value       interface{} `json:"value"`
					Unit        string      `json:"unit"`
				}{
					{
						ChannelID:   controlReq.ChannelID,
						ChannelName: controlReq.ChannelName,
						Value:       controlReq.Value,
						Unit:        "C", // Default unit
					},
				},
			},
		},
	}

	// Send to IO
	if ac.ioChannel != nil {
		return ac.ioChannel.SendWriteRequest(writeReq)
	}

	return nil
}

// handlePullSensorData handles pull sensor data requests from cloud
func (ac *AggregatorClientImpl) handlePullSensorData(payload []byte) error {
	var pullReq shared.ReadRequest
	if err := json.Unmarshal(payload, &pullReq); err != nil {
		return err
	}

	// Check if we have recent periodic data
	ac.periodicDataMutex.Lock()
	hasRecentData := time.Since(ac.periodicDataStore.Timestamp) <= shared.GetPDSTime()
	storedData := ac.periodicDataStore.Data
	ac.periodicDataMutex.Unlock()

	if hasRecentData {
		log.Println("Aggregator: returning recent periodic data")
		// Filter the stored data based on request type
		filteredData := ac.dataTransformer.FilterPeriodicData(storedData, pullReq)

		// Check if filtered data has any zones
		if len(filteredData.Zones) > 0 {
			topic := ac.getTopicConstant("TopicArgusPeriodic")
			return ac.SendToCloud(topic, filteredData)
		}
		log.Println("Aggregator: filtered data has no zones, proceeding with normal request flow")
	}
	log.Println("Aggregator: no recent periodic data found, proceeding with normal request flow")

	// Handle different request types with validation
	switch pullReq.RequestType {
	case "zone":
		return ac.handleZoneRequest(pullReq)
	case "card":
		return ac.handleCardRequest(pullReq)
	case "channel":
		return ac.handleChannelRequest(pullReq)
	default:
		return ac.handleOtherRequests(pullReq)
	}
}

// handleZoneRequest handles zone-specific pull requests
func (ac *AggregatorClientImpl) handleZoneRequest(pullReq shared.ReadRequest) error {
	// Validate the zone request
	if err := ac.validator.ValidateZoneRequest(pullReq); err != nil {
		log.Printf("Aggregator: Zone request validation failed: %v", err)
		return nil // Don't return error, just ignore invalid requests
	}

	// Get all cards for the requested zone
	zoneId := strings.TrimSpace(pullReq.Value)
	cards := shared.GetCardsForZone(zoneId)

	if len(cards) == 0 {
		log.Printf("Aggregator: No cards found for zone: %s", zoneId)
		return nil
	}

	log.Printf("Aggregator: Found %d cards for zone %s: %v", len(cards), zoneId, cards)

	// Create cards request
	cardsReq := shared.CardsReadRequest{
		RequestType: "cards",
		Values:      cards,
	}

	// Send to IO
	if ac.ioChannel != nil {
		return ac.ioChannel.SendReadRequest(cardsReq)
	}

	return nil
}

// handleCardRequest handles card-specific pull requests
func (ac *AggregatorClientImpl) handleCardRequest(pullReq shared.ReadRequest) error {
	// Validate the card request
	if err := ac.validator.ValidateCardRequest(pullReq); err != nil {
		log.Printf("Aggregator: Card request validation failed: %v", err)
		return nil // Don't return error, just ignore invalid requests
	}

	// Extract cardId from zone/cardId format
	parts := strings.Split(pullReq.Value, "/")
	if len(parts) != 2 {
		log.Printf("Aggregator: Invalid card request format: %s", pullReq.Value)
		return nil
	}

	cardId := parts[1]

	// Create read request for IO module
	readReq := shared.ReadRequest{
		RequestType: "card",
		Value:       cardId,
	}

	// Send to IO
	if ac.ioChannel != nil {
		return ac.ioChannel.SendReadRequest(readReq)
	}

	return nil
}

// handleChannelRequest handles channel-specific pull requests
func (ac *AggregatorClientImpl) handleChannelRequest(pullReq shared.ReadRequest) error {
	// Validate the channel request
	if err := ac.validator.ValidateChannelRequest(pullReq); err != nil {
		log.Printf("Aggregator: Channel request validation failed: %v", err)
		return nil // Don't return error, just ignore invalid requests
	}

	// Extract cardId/channelId from zone/cardId/channelId format
	parts := strings.Split(pullReq.Value, "/")
	if len(parts) != 3 {
		log.Printf("Aggregator: Invalid channel request format: %s", pullReq.Value)
		return nil
	}

	cardId := parts[1]
	channelId := parts[2]

	// Create read request for IO module
	readReq := shared.ReadRequest{
		RequestType: "channel",
		Value:       cardId + "/" + channelId,
	}

	// Send to IO
	if ac.ioChannel != nil {
		return ac.ioChannel.SendReadRequest(readReq)
	}

	return nil
}

// handleOtherRequests handles other types of pull requests
func (ac *AggregatorClientImpl) handleOtherRequests(pullReq shared.ReadRequest) error {
	// Handle other request types (card, channel, all) with existing logic
	readReq := shared.ReadRequest{
		RequestType: pullReq.RequestType,
		Value:       pullReq.Value,
	}

	// Adjust value format based on request type
	switch pullReq.RequestType {
	case "card":
		if parts := strings.Split(pullReq.Value, "/"); len(parts) == 2 {
			readReq.Value = parts[1]
		}
	case "channel":
		if parts := strings.Split(pullReq.Value, "/"); len(parts) == 3 {
			readReq.Value = parts[1] + "/" + parts[2]
		}
	}

	// Send to IO
	if ac.ioChannel != nil {
		return ac.ioChannel.SendReadRequest(readReq)
	}

	return nil
}

// SendToCloud sends data to cloud
func (ac *AggregatorClientImpl) SendToCloud(topic string, data interface{}) error {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return err
	}

	if token := ac.client.Publish(topic, shared.GetQoS(), false, jsonData); token.Wait() && token.Error() != nil {
		return token.Error()
	}

	log.Printf("Aggregator: ✓ Sent to cloud topic %s", topic)
	return nil
}

// PublishInitialConfig publishes initial configuration to cloud
func (ac *AggregatorClientImpl) PublishInitialConfig() error {
	// Load and publish initial config on startup
	ioConfig, err := ac.configManager.GetIOConfig()
	if err != nil {
		log.Printf("Aggregator: Warning: Cannot load IO firmware config for initial publish: %v", err)
		return err
	}

	cloudConfig := ac.dataTransformer.TransformConfigToCloud(ioConfig)
	topic := ac.getTopicConstant("TopicArgusConfig")

	return ac.SendToCloud(topic, cloudConfig)
}

// filterConfigByZone filters cloud config by zone
func (ac *AggregatorClientImpl) filterConfigByZone(cloudConfig *shared.CloudPlatformConfig, value string) *shared.CloudPlatformConfig {
	// Extract zone ID from value (format: "* / ${zoneId}" or just zoneId)
	zoneId := strings.TrimSpace(value)
	if strings.Contains(zoneId, "/") {
		parts := strings.Split(zoneId, "/")
		if len(parts) > 1 {
			zoneId = strings.TrimSpace(parts[1])
		}
	}
	zoneId = strings.Trim(zoneId, "${}")

	// Filter zones
	filteredZones := []shared.CloudZone{}
	for _, zone := range cloudConfig.Zones {
		if zone.ZoneID == zoneId {
			filteredZones = append(filteredZones, zone)
			break
		}
	}
	cloudConfig.Zones = filteredZones
	return cloudConfig
}

// getTopicConstant gets topic constant from config
func (ac *AggregatorClientImpl) getTopicConstant(topicName string) string {
	// Check subscribe topics first
	if topic, ok := ac.config.SubscribeTopics[topicName]; ok {
		return topic
	}
	// Check publish topics
	if topic, ok := ac.config.PublishTopics[topicName]; ok {
		return topic
	}
	return ""
}

// onConnect handles connection events
func (ac *AggregatorClientImpl) onConnect(client mqtt.Client) {
	log.Printf("Aggregator: Connected to cloud MQTT broker")
	ac.serverWrapper.SubscribeToTopics()

	// Publish initial config
	if err := ac.PublishInitialConfig(); err != nil {
		log.Printf("Aggregator: Error publishing initial config: %v", err)
	}
}

// onConnectionLost handles connection lost events
func (ac *AggregatorClientImpl) onConnectionLost(client mqtt.Client, err error) {
	log.Printf("Aggregator: Connection to cloud lost: %v", err)
}

// StorePeriodicData stores periodic data for recent data requests
func (ac *AggregatorClientImpl) StorePeriodicData(data shared.CloudPeriodicData) {
	ac.periodicDataMutex.Lock()
	timestamp := time.Now()
	if data.Timestamp > 0 {
		timestamp = time.Unix(0, data.Timestamp*int64(time.Millisecond))
	}
	ac.periodicDataStore = shared.PeriodicDataStore{
		Data:      data,
		Timestamp: timestamp,
	}
	ac.periodicDataMutex.Unlock()
}
